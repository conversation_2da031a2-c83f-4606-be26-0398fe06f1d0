import { Server as SocketIOServer, Socket } from 'socket.io'
import { AuthService, JWTPayload } from '@/services/auth'
import { SessionManager, PubSubManager } from '@/config/redis'
import { UAUIEventUnion, UAUIEventFactory } from '@/types/uaui-protocol'
import { logger } from '@/utils/logger'
import { prisma } from '@/config/database'
import { v4 as uuidv4 } from 'uuid'

interface AuthenticatedSocket extends Socket {
  user: JWTPayload
  sessionId: string
}

interface SocketSession {
  socketId: string
  userId: string
  sessionId: string
  connectedAt: Date
  lastActivity: Date
}

export class APXWebSocketGateway {
  private io: SocketIOServer
  private activeSessions = new Map<string, SocketSession>()
  private userSockets = new Map<string, Set<string>>()

  constructor(io: SocketIOServer) {
    this.io = io
    this.setupEventHandlers()
    this.setupHeartbeat()
  }

  private setupEventHandlers() {
    this.io.use(this.authenticateSocket.bind(this))
    this.io.on('connection', this.handleConnection.bind(this))

    // Subscribe to Redis events for cross-server synchronization
    PubSubManager.subscribeToEvents(this.handleRedisEvent.bind(this))
  }

  private async authenticateSocket(socket: Socket, next: (err?: Error) => void) {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '')

      if (!token) {
        return next(new Error('No authentication token provided'))
      }

      const payload = AuthService.verifyToken(token)
      const user = await AuthService.getUserById(payload.userId)

      if (!user) {
        return next(new Error('User not found'))
      }

      ; (socket as AuthenticatedSocket).user = payload
        ; (socket as AuthenticatedSocket).sessionId = socket.handshake.query.sessionId as string || uuidv4()

      next()
    } catch (error) {
      logger.error('Socket authentication failed:', error)
      next(new Error('Authentication failed'))
    }
  }

  private async handleConnection(socket: Socket) {
    const authSocket = socket as AuthenticatedSocket
    const { user, sessionId } = authSocket

    logger.info(`User ${user.userId} connected with session ${sessionId}`)

    // Register session
    await this.registerSession(authSocket)

    // Join user-specific room
    socket.join(`user:${user.userId}`)
    socket.join(`session:${sessionId}`)

    // Subscribe to user and session events
    await this.subscribeToUserEvents(authSocket)

    // Send connection confirmation
    socket.emit('connected', {
      sessionId,
      userId: user.userId,
      timestamp: new Date().toISOString()
    })

    // Handle events
    socket.on('agent.start', this.handleAgentStart.bind(this, authSocket))
    socket.on('agent.stop', this.handleAgentStop.bind(this, authSocket))
    socket.on('message.send', this.handleUserMessage.bind(this, authSocket))
    socket.on('tool.execute', this.handleToolExecution.bind(this, authSocket))
    socket.on('session.update', this.handleSessionUpdate.bind(this, authSocket))

    // Handle disconnection
    socket.on('disconnect', this.handleDisconnection.bind(this, authSocket))

    // Update activity
    socket.on('heartbeat', () => this.updateActivity(authSocket))
  }

  private async registerSession(socket: AuthenticatedSocket) {
    const { user, sessionId } = socket

    const session: SocketSession = {
      socketId: socket.id,
      userId: user.userId,
      sessionId,
      connectedAt: new Date(),
      lastActivity: new Date()
    }

    this.activeSessions.set(socket.id, session)

    // Track user sockets
    if (!this.userSockets.has(user.userId)) {
      this.userSockets.set(user.userId, new Set())
    }
    this.userSockets.get(user.userId)!.add(socket.id)

    // Store session in Redis
    await SessionManager.setSession(sessionId, {
      userId: user.userId,
      socketId: socket.id,
      connectedAt: session.connectedAt.toISOString(),
      status: 'active'
    })

    await SessionManager.addUserSession(user.userId, sessionId)

    // Create session start event
    const event = UAUIEventFactory.createSessionStart(sessionId, user.userId)
    await this.publishEvent(event)
  }

  private async subscribeToUserEvents(socket: AuthenticatedSocket) {
    const { user, sessionId } = socket

    // Subscribe to session-specific events
    PubSubManager.subscribeToSession(sessionId, (event) => {
      socket.emit('event', event)
    })

    // Subscribe to user-specific events
    PubSubManager.subscribeToUser(user.userId, (event) => {
      socket.emit('event', event)
    })
  }

  private async handleAgentStart(socket: AuthenticatedSocket, data: any) {
    const { user, sessionId } = socket

    try {
      // Validate agent exists and user has access
      const agent = await prisma.agent.findFirst({
        where: {
          id: data.agentId,
          OR: [
            { userId: user.userId },
            ...(user.tenantId !== undefined ? [{ tenantId: user.tenantId }] : [])
          ]
        }
      })

      if (!agent) {
        socket.emit('error', { message: 'Agent not found or access denied' })
        return
      }

      // Create agent start event
      const event = UAUIEventFactory.createAgentStart(sessionId, data.agentId, agent.config)
      await this.publishEvent(event)

      // Update session context
      await SessionManager.updateContext(sessionId, {
        activeAgent: data.agentId,
        agentStartedAt: new Date().toISOString()
      })

    } catch (error) {
      logger.error('Agent start failed:', error)
      socket.emit('error', { message: 'Failed to start agent' })
    }
  }

  private async handleAgentStop(socket: AuthenticatedSocket, data: any) {
    const { sessionId } = socket

    try {
      const event = {
        id: uuidv4(),
        type: 'agent.stop' as const,
        timestamp: new Date().toISOString(),
        sessionId,
        agentId: data.agentId,
        data: {
          agentId: data.agentId,
          reason: data.reason || 'user_stop',
          result: data.result
        }
      }

      await this.publishEvent(event)

      // Update session context
      await SessionManager.updateContext(sessionId, {
        activeAgent: null,
        agentStoppedAt: new Date().toISOString()
      })

    } catch (error) {
      logger.error('Agent stop failed:', error)
      socket.emit('error', { message: 'Failed to stop agent' })
    }
  }

  private async handleUserMessage(socket: AuthenticatedSocket, data: any) {
    const { user, sessionId } = socket

    try {
      const event = UAUIEventFactory.createUserMessage(sessionId, user.userId, data.content)
      await this.publishEvent(event)

      // Store message in session memory
      await SessionManager.addMemory(sessionId, {
        type: 'user_message',
        content: data.content,
        timestamp: new Date().toISOString()
      })

    } catch (error) {
      logger.error('User message failed:', error)
      socket.emit('error', { message: 'Failed to send message' })
    }
  }

  private async handleToolExecution(socket: AuthenticatedSocket, data: any) {
    const { sessionId } = socket

    try {
      const event = UAUIEventFactory.createToolCall(
        sessionId,
        data.agentId,
        data.toolId,
        data.toolName,
        data.input
      )

      await this.publishEvent(event)

    } catch (error) {
      logger.error('Tool execution failed:', error)
      socket.emit('error', { message: 'Failed to execute tool' })
    }
  }

  private async handleSessionUpdate(socket: AuthenticatedSocket, data: any) {
    const { sessionId } = socket

    try {
      await SessionManager.updateContext(sessionId, data.context || {})

      socket.emit('session.updated', {
        sessionId,
        timestamp: new Date().toISOString()
      })

    } catch (error) {
      logger.error('Session update failed:', error)
      socket.emit('error', { message: 'Failed to update session' })
    }
  }

  private async handleDisconnection(socket: AuthenticatedSocket) {
    const { user, sessionId } = socket

    logger.info(`User ${user.userId} disconnected from session ${sessionId}`)

    // Clean up session tracking
    this.activeSessions.delete(socket.id)

    const userSockets = this.userSockets.get(user.userId)
    if (userSockets) {
      userSockets.delete(socket.id)
      if (userSockets.size === 0) {
        this.userSockets.delete(user.userId)
      }
    }

    // Unsubscribe from events
    await PubSubManager.unsubscribeFromSession(sessionId)
    await PubSubManager.unsubscribeFromUser(user.userId)

    // Update session status
    await SessionManager.updateContext(sessionId, {
      status: 'disconnected',
      disconnectedAt: new Date().toISOString()
    })

    // Create session end event
    const event = {
      id: uuidv4(),
      type: 'session.end' as const,
      timestamp: new Date().toISOString(),
      sessionId,
      userId: user.userId,
      data: {
        sessionId,
        duration: Date.now() - this.activeSessions.get(socket.id)?.connectedAt.getTime() || 0,
        reason: 'user_disconnect'
      }
    }

    await this.publishEvent(event)
  }

  private updateActivity(socket: AuthenticatedSocket) {
    const session = this.activeSessions.get(socket.id)
    if (session) {
      session.lastActivity = new Date()
    }
  }

  private async handleRedisEvent(event: UAUIEventUnion) {
    // Broadcast event to relevant sockets
    if (event.sessionId) {
      this.io.to(`session:${event.sessionId}`).emit('event', event)
    }

    if (event.userId) {
      this.io.to(`user:${event.userId}`).emit('event', event)
    }
  }

  private async publishEvent(event: UAUIEventUnion) {
    // Store event in database
    await prisma.event.create({
      data: {
        id: event.id,
        sessionId: event.sessionId,
        type: event.type,
        data: event.data,
        timestamp: new Date(event.timestamp)
      }
    })

    // Publish to Redis for cross-server sync
    await PubSubManager.publishEvent(event)
  }

  private setupHeartbeat() {
    setInterval(() => {
      const now = new Date()
      const timeout = 5 * 60 * 1000 // 5 minutes

      for (const [socketId, session] of this.activeSessions.entries()) {
        if (now.getTime() - session.lastActivity.getTime() > timeout) {
          const socket = this.io.sockets.sockets.get(socketId)
          if (socket) {
            socket.disconnect(true)
          }
        }
      }
    }, 60000) // Check every minute
  }

  // Public methods for external use
  public async broadcastToUser(userId: string, event: UAUIEventUnion) {
    this.io.to(`user:${userId}`).emit('event', event)
  }

  public async broadcastToSession(sessionId: string, event: UAUIEventUnion) {
    this.io.to(`session:${sessionId}`).emit('event', event)
  }

  public getActiveSessionsCount(): number {
    return this.activeSessions.size
  }

  public getUserSessionsCount(userId: string): number {
    return this.userSockets.get(userId)?.size || 0
  }
}

export function setupWebSocket(io: SocketIOServer): APXWebSocketGateway {
  return new APXWebSocketGateway(io)
}
