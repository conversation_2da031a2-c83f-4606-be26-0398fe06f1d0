import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { toast } from 'react-hot-toast'
import {
  ApiResponse,
  PaginatedResponse,
  User,
  AuthResponse,
  Agent,
  Tool,
  Provider,
  Session,
  Event,
  LoginForm,
  RegisterForm,
  AgentForm,
  ToolForm,
  ProviderForm
} from '@/types'

class ApiClient {
  private client: AxiosInstance
  private token: string | null = null

  constructor() {
    this.client = axios.create({
      baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
    this.loadToken()
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        return response
      },
      async (error) => {
        const originalRequest = error.config

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true

          try {
            await this.refreshToken()
            return this.client(originalRequest)
          } catch (refreshError) {
            this.logout()
            window.location.href = '/login'
            return Promise.reject(refreshError)
          }
        }

        // Handle other errors
        if (error.response?.data?.error) {
          toast.error(error.response.data.error)
        } else if (error.message) {
          toast.error(error.message)
        }

        return Promise.reject(error)
      }
    )
  }

  private loadToken() {
    this.token = localStorage.getItem('auth_token')
  }

  private saveToken(token: string) {
    this.token = token
    localStorage.setItem('auth_token', token)
  }

  private removeToken() {
    this.token = null
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
  }

  // Auth methods
  async login(credentials: LoginForm): Promise<AuthResponse> {
    const response = await this.client.post<ApiResponse<AuthResponse>>('/auth/login', credentials)
    const { token, refreshToken } = response.data.data!

    this.saveToken(token)
    localStorage.setItem('refresh_token', refreshToken)

    return response.data.data!
  }

  async register(userData: RegisterForm): Promise<AuthResponse> {
    const response = await this.client.post<ApiResponse<AuthResponse>>('/auth/register', userData)
    const { token, refreshToken } = response.data.data!

    this.saveToken(token)
    localStorage.setItem('refresh_token', refreshToken)

    return response.data.data!
  }

  async refreshToken(): Promise<void> {
    const refreshToken = localStorage.getItem('refresh_token')
    if (!refreshToken) throw new Error('No refresh token')

    const response = await this.client.post<ApiResponse<{ token: string; refreshToken: string }>>('/auth/refresh', {
      refreshToken
    })

    const { token, refreshToken: newRefreshToken } = response.data.data!
    this.saveToken(token)
    localStorage.setItem('refresh_token', newRefreshToken)
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.client.get<ApiResponse<User>>('/auth/me')
    return response.data.data!
  }

  logout() {
    this.removeToken()
  }

  // Agent methods
  async getAgents(): Promise<Agent[]> {
    const response = await this.client.get<ApiResponse<Agent[]>>('/agents')
    return response.data.data!
  }

  async getAgent(id: string): Promise<Agent> {
    const response = await this.client.get<ApiResponse<Agent>>(`/agents/${id}`)
    return response.data.data!
  }

  async createAgent(data: AgentForm): Promise<Agent> {
    const response = await this.client.post<ApiResponse<Agent>>('/agents', data)
    return response.data.data!
  }

  async updateAgent(id: string, data: Partial<AgentForm>): Promise<Agent> {
    const response = await this.client.put<ApiResponse<Agent>>(`/agents/${id}`, data)
    return response.data.data!
  }

  async deleteAgent(id: string): Promise<void> {
    await this.client.delete(`/agents/${id}`)
  }

  async assignToolToAgent(agentId: string, toolId: string, config?: any, priority = 0): Promise<void> {
    await this.client.post(`/agents/${agentId}/tools`, {
      toolId,
      config,
      priority
    })
  }

  async removeToolFromAgent(agentId: string, toolId: string): Promise<void> {
    await this.client.delete(`/agents/${agentId}/tools/${toolId}`)
  }

  async getAgentStatus(id: string): Promise<any> {
    const response = await this.client.get<ApiResponse<any>>(`/agents/${id}/status`)
    return response.data.data!
  }

  // Tool methods
  async getTools(): Promise<Tool[]> {
    const response = await this.client.get<ApiResponse<Tool[]>>('/tools')
    return response.data.data!
  }

  async getTool(id: string): Promise<Tool> {
    const response = await this.client.get<ApiResponse<Tool>>(`/tools/${id}`)
    return response.data.data!
  }

  async createTool(data: ToolForm): Promise<Tool> {
    const response = await this.client.post<ApiResponse<Tool>>('/tools', data)
    return response.data.data!
  }

  async updateTool(id: string, data: Partial<ToolForm>): Promise<Tool> {
    const response = await this.client.put<ApiResponse<Tool>>(`/tools/${id}`, data)
    return response.data.data!
  }

  async deleteTool(id: string): Promise<void> {
    await this.client.delete(`/tools/${id}`)
  }

  async executeTool(id: string, input: any, sessionId?: string): Promise<any> {
    const response = await this.client.post<ApiResponse<any>>(`/tools/${id}/execute`, {
      input,
      sessionId
    })
    return response.data.data!
  }

  async getToolStats(id: string): Promise<any> {
    const response = await this.client.get<ApiResponse<any>>(`/tools/${id}/stats`)
    return response.data.data!
  }

  async getToolSchemas(): Promise<any> {
    const response = await this.client.get<ApiResponse<any>>('/tools/types/schemas')
    return response.data.data!
  }

  // Provider methods
  async getProviders(): Promise<Provider[]> {
    const response = await this.client.get<ApiResponse<Provider[]>>('/providers')
    return response.data.data!
  }

  async getProvider(id: string): Promise<Provider> {
    const response = await this.client.get<ApiResponse<Provider>>(`/providers/${id}`)
    return response.data.data!
  }

  async createProvider(data: ProviderForm): Promise<Provider> {
    const response = await this.client.post<ApiResponse<Provider>>('/providers', data)
    return response.data.data!
  }

  async updateProvider(id: string, data: Partial<ProviderForm>): Promise<Provider> {
    const response = await this.client.put<ApiResponse<Provider>>(`/providers/${id}`, data)
    return response.data.data!
  }

  async deleteProvider(id: string): Promise<void> {
    await this.client.delete(`/providers/${id}`)
  }

  async testProvider(id: string, messages: any[], config?: any): Promise<any> {
    const response = await this.client.post<ApiResponse<any>>(`/providers/${id}/test`, {
      messages,
      config
    })
    return response.data.data!
  }

  async getProviderHealth(id: string): Promise<any> {
    const response = await this.client.get<ApiResponse<any>>(`/providers/${id}/health`)
    return response.data.data!
  }

  async selectBestProvider(messages: any[], requirements?: any): Promise<any> {
    const response = await this.client.post<ApiResponse<any>>('/providers/select', {
      messages,
      requirements
    })
    return response.data.data!
  }

  async getProviderStats(id: string): Promise<any> {
    const response = await this.client.get<ApiResponse<any>>(`/providers/${id}/stats`)
    return response.data.data!
  }

  // Session methods
  async getSessions(params?: { limit?: number; offset?: number; status?: string; agentId?: string }): Promise<PaginatedResponse<Session>> {
    const response = await this.client.get<PaginatedResponse<Session>>('/sessions', { params })
    return response.data
  }

  async getSession(id: string): Promise<Session> {
    const response = await this.client.get<ApiResponse<Session>>(`/sessions/${id}`)
    return response.data.data!
  }

  async createSession(data: { agentId?: string; providerId?: string; context?: any; metadata?: any }): Promise<Session> {
    const response = await this.client.post<ApiResponse<Session>>('/sessions', data)
    return response.data.data!
  }

  async updateSession(id: string, data: { context?: any; metadata?: any; status?: string }): Promise<Session> {
    const response = await this.client.put<ApiResponse<Session>>(`/sessions/${id}`, data)
    return response.data.data!
  }

  async deleteSession(id: string): Promise<void> {
    await this.client.delete(`/sessions/${id}`)
  }

  async startAgentInSession(sessionId: string, agentId: string, variables?: any): Promise<void> {
    await this.client.post(`/sessions/${sessionId}/start-agent`, {
      agentId,
      variables
    })
  }

  async stopAgentInSession(sessionId: string): Promise<void> {
    await this.client.post(`/sessions/${sessionId}/stop-agent`)
  }

  async getSessionEvents(sessionId: string, params?: { type?: string; limit?: number; offset?: number }): Promise<PaginatedResponse<Event>> {
    const response = await this.client.get<PaginatedResponse<Event>>(`/sessions/${sessionId}/events`, { params })
    return response.data
  }

  async getSessionContext(sessionId: string): Promise<any> {
    const response = await this.client.get<ApiResponse<any>>(`/sessions/${sessionId}/context`)
    return response.data.data!
  }

  // Generic request method
  async request<T = any>(config: AxiosRequestConfig): Promise<T> {
    const response = await this.client.request<T>(config)
    return response.data
  }

  // File upload method
  async uploadFile(file: File, endpoint: string, onProgress?: (progress: number) => void): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await this.client.post(endpoint, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })

    return response.data
  }

  // Health check
  async healthCheck(): Promise<any> {
    const response = await this.client.get('/health')
    return response.data
  }
}

export const api = new ApiClient()
export default api
