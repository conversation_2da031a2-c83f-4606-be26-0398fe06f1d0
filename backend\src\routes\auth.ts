import { Router } from 'express'
import { z } from 'zod'
import { AuthService } from '@/services/auth'
import { logger } from '@/utils/logger'

const router = Router()

// Validation schemas
const RegisterSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  name: z.string().optional(),
  tenantId: z.string().optional()
})

const LoginSchema = z.object({
  email: z.string().email(),
  password: z.string()
})

const RefreshTokenSchema = z.object({
  refreshToken: z.string()
})

// Register
router.post('/register', async (req, res, next) => {
  try {
    const { email, password, name, tenantId } = RegisterSchema.parse(req.body)

    const result = await AuthService.register(email, password, name, tenantId)

    logger.info(`User registered: ${email}`)

    res.status(201).json({
      success: true,
      data: result
    })
  } catch (error) {
    next(error)
  }
})

// Login
router.post('/login', async (req, res, next) => {
  try {
    const { email, password } = LoginSchema.parse(req.body)

    const result = await AuthService.login(email, password)

    logger.info(`User logged in: ${email}`)

    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    next(error)
  }
})

// Refresh token
router.post('/refresh', async (req, res, next) => {
  try {
    const { refreshToken } = RefreshTokenSchema.parse(req.body)

    const result = await AuthService.refreshToken(refreshToken)

    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    next(error)
  }
})

// Get current user
router.get('/me', async (req, res, next): Promise<void> => {
  try {
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'No token provided' })
      return
    }

    const token = authHeader.substring(7)
    const payload = AuthService.verifyToken(token)
    const user = await AuthService.getUserById(payload.userId)

    if (!user) {
      res.status(404).json({ error: 'User not found' })
      return
    }

    res.json({
      success: true,
      data: user
    })
  } catch (error) {
    next(error)
  }
})

// Logout (client-side token invalidation)
router.post('/logout', (req, res) => {
  res.json({
    success: true,
    message: 'Logged out successfully'
  })
})

export default router
