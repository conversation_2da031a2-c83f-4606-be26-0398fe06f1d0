import { useState, useEffect } from 'react'
import { ChevronUpIcon, ChatBubbleLeftRightIcon, QuestionMarkCircleIcon } from '@heroicons/react/24/outline'

const FloatingActionButton = () => {
  const [isVisible, setIsVisible] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true)
      } else {
        setIsVisible(false)
        setIsExpanded(false)
      }
    }

    window.addEventListener('scroll', toggleVisibility)
    return () => window.removeEventListener('scroll', toggleVisibility)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  const actions = [
    {
      icon: ChatBubbleLeftRightIcon,
      label: 'Chat Support',
      action: () => console.log('Open chat'),
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      icon: QuestionMarkCircleIcon,
      label: 'Help Center',
      action: () => console.log('Open help'),
      color: 'bg-purple-500 hover:bg-purple-600'
    },
    {
      icon: ChevronUpIcon,
      label: 'Back to Top',
      action: scrollToTop,
      color: 'bg-blue-500 hover:bg-blue-600'
    }
  ]

  if (!isVisible) return null

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Action buttons */}
      <div className={`flex flex-col space-y-3 mb-3 transition-all duration-300 ${isExpanded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4 pointer-events-none'}`}>
        {actions.slice(0, -1).map((action, index) => (
          <button
            key={index}
            onClick={action.action}
            className={`group relative w-12 h-12 ${action.color} text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-110 flex items-center justify-center`}
            style={{ transitionDelay: `${index * 50}ms` }}
          >
            <action.icon className="w-6 h-6" />
            <span className="absolute right-14 bg-slate-900 text-white px-3 py-1 rounded-lg text-sm whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
              {action.label}
            </span>
          </button>
        ))}
      </div>

      {/* Main FAB */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-14 h-14 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-full shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 hover:scale-110 flex items-center justify-center group animate-pulse hover:animate-none"
      >
        <ChevronUpIcon 
          className={`w-6 h-6 transition-transform duration-300 ${isExpanded ? 'rotate-180' : ''}`} 
          onClick={(e) => {
            e.stopPropagation()
            if (!isExpanded) {
              scrollToTop()
            }
          }}
        />
      </button>
    </div>
  )
}

export default FloatingActionButton
