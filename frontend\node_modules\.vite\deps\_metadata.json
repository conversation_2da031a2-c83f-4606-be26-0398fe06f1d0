{"hash": "dd76e883", "browserHash": "10323629", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "cf9b1de1", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "fdf59ec4", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "ea4fc2e7", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "0cfb521b", "needsInterop": true}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "27d52cef", "needsInterop": false}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "9e829016", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "b9322e91", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "04dc9f56", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "0f801138", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "1feabb46", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "f9c8790c", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "af6d2c48", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "8a28041f", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "2f1623ca", "needsInterop": false}, "date-fns": {"src": "../../date-fns/esm/index.js", "file": "date-fns.js", "fileHash": "01174f92", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "d43fd946", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "cfbd0b04", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "57801739", "needsInterop": false}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "b8c1c0eb", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "e47ee00f", "needsInterop": false}, "socket.io-client": {"src": "../../socket.io-client/build/esm/index.js", "file": "socket__io-client.js", "fileHash": "25bfa311", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "9e0c57c4", "needsInterop": false}, "zod": {"src": "../../zod/index.js", "file": "zod.js", "fileHash": "a975776e", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "57d6f8d8", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "c118a5ec", "needsInterop": false}}, "chunks": {"chunk-AJTCXCUR": {"file": "chunk-AJTCXCUR.js"}, "chunk-JMFKCUDV": {"file": "chunk-JMFKCUDV.js"}, "chunk-ESKSE55X": {"file": "chunk-ESKSE55X.js"}, "chunk-QP3YXCQR": {"file": "chunk-QP3YXCQR.js"}, "chunk-MFTD2LE2": {"file": "chunk-MFTD2LE2.js"}, "chunk-UBDHIGUN": {"file": "chunk-UBDHIGUN.js"}, "chunk-2PMNUQTO": {"file": "chunk-2PMNUQTO.js"}, "chunk-KGUGL44G": {"file": "chunk-KGUGL44G.js"}, "chunk-WS6WXAIR": {"file": "chunk-WS6WXAIR.js"}}}