{"hash": "92284769", "browserHash": "04bf3762", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "7ba650ed", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "2e7da7f1", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "29ff81eb", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "68211dfb", "needsInterop": true}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "3c66e599", "needsInterop": false}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "6482ff4e", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "8d09c5e0", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "b198e997", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "b1bc3d76", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "5c712157", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "2d3c7e34", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "cc696242", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "4dce9919", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "f29b0507", "needsInterop": false}, "date-fns": {"src": "../../date-fns/esm/index.js", "file": "date-fns.js", "fileHash": "1a288438", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "ada31c2e", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "11e8d8f3", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "6e01df3d", "needsInterop": false}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "e2e84dea", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "adbd0e78", "needsInterop": false}, "socket.io-client": {"src": "../../socket.io-client/build/esm/index.js", "file": "socket__io-client.js", "fileHash": "983284de", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "4e51d20d", "needsInterop": false}, "zod": {"src": "../../zod/index.js", "file": "zod.js", "fileHash": "92a1ece9", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "86473c4f", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "b20ad3d6", "needsInterop": false}}, "chunks": {"chunk-ESKSE55X": {"file": "chunk-ESKSE55X.js"}, "chunk-AJTCXCUR": {"file": "chunk-AJTCXCUR.js"}, "chunk-QP3YXCQR": {"file": "chunk-QP3YXCQR.js"}, "chunk-MFTD2LE2": {"file": "chunk-MFTD2LE2.js"}, "chunk-JMFKCUDV": {"file": "chunk-JMFKCUDV.js"}, "chunk-UBDHIGUN": {"file": "chunk-UBDHIGUN.js"}, "chunk-2PMNUQTO": {"file": "chunk-2PMNUQTO.js"}, "chunk-KGUGL44G": {"file": "chunk-KGUGL44G.js"}, "chunk-WS6WXAIR": {"file": "chunk-WS6WXAIR.js"}}}