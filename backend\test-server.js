// Simple test server to check if basic functionality works
console.log('Starting test server...')

import express from 'express'
import cors from 'cors'

const app = express()
const port = 3001

// Middleware
app.use(cors())
app.use(express.json())

// Test route
app.get('/api/test', (req, res) => {
  res.json({ message: 'Backend server is working!' })
})

// Auth test route
app.get('/api/auth/me', (req, res) => {
  res.json({
    success: true,
    data: {
      id: 'test-user',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'USER',
      tenantId: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  })
})

app.listen(port, () => {
  console.log(`✅ Test server running on port ${port}`)
  console.log(`🔗 API: http://localhost:${port}/api`)
})
