import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { ArrowRightIcon, PlayIcon, Bars3Icon, XMarkIcon, CheckIcon } from '@heroicons/react/24/outline'
import { SunIcon, MoonIcon } from '@heroicons/react/24/solid'
import { useTheme } from '@/contexts/ThemeContext'

const LandingPage = () => {
  const { theme, toggleTheme } = useTheme()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'dark' : ''}`}>
      {/* Navigation */}
      <nav className="bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl font-bold text-blue-600">SynapseAI</span>
              </div>
            </div>
            
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-8">
                <a href="#features" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 px-3 py-2 text-sm font-medium">Features</a>
                <a href="#pricing" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 px-3 py-2 text-sm font-medium">Pricing</a>
                <a href="#about" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 px-3 py-2 text-sm font-medium">About</a>
                <button
                  onClick={toggleTheme}
                  className="p-2 rounded-md text-slate-600 dark:text-slate-300 hover:text-blue-600"
                >
                  {theme === 'light' ? <MoonIcon className="h-5 w-5" /> : <SunIcon className="h-5 w-5" />}
                </button>
                <Link to="/login" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 px-3 py-2 text-sm font-medium">
                  Sign in
                </Link>
                <Link to="/register" className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                  Get started
                </Link>
              </div>
            </div>

            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-slate-600 dark:text-slate-300 hover:text-blue-600"
              >
                {isMenuOpen ? <XMarkIcon className="h-6 w-6" /> : <Bars3Icon className="h-6 w-6" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white dark:bg-slate-900 border-t border-slate-200 dark:border-slate-700">
              <a href="#features" className="block px-3 py-2 text-base font-medium text-slate-600 dark:text-slate-300 hover:text-blue-600">Features</a>
              <a href="#pricing" className="block px-3 py-2 text-base font-medium text-slate-600 dark:text-slate-300 hover:text-blue-600">Pricing</a>
              <a href="#about" className="block px-3 py-2 text-base font-medium text-slate-600 dark:text-slate-300 hover:text-blue-600">About</a>
              <Link to="/login" className="block px-3 py-2 text-base font-medium text-slate-600 dark:text-slate-300 hover:text-blue-600">Sign in</Link>
              <Link to="/register" className="block px-3 py-2 text-base font-medium bg-blue-600 text-white rounded-md hover:bg-blue-700">Get started</Link>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="bg-white dark:bg-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 dark:text-white">
              AI that works for your business
            </h1>
            <p className="mt-6 text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              Transform your operations with intelligent automation. SynapseAI learns your business patterns and scales with your growth.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/register"
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                Start free trial
                <ArrowRightIcon className="ml-2 h-5 w-5" />
              </Link>
              <button className="inline-flex items-center px-6 py-3 border border-slate-300 dark:border-slate-600 text-base font-medium rounded-md text-slate-700 dark:text-slate-300 bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700">
                <PlayIcon className="mr-2 h-5 w-5" />
                Watch demo
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white">
              Everything you need to automate your business
            </h2>
            <p className="mt-4 text-lg text-slate-600 dark:text-slate-300">
              Powerful AI tools that integrate seamlessly with your existing workflow
            </p>
          </div>

          <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="mx-auto h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="mt-6 text-lg font-medium text-slate-900 dark:text-white">Lightning Fast</h3>
              <p className="mt-2 text-base text-slate-600 dark:text-slate-300">
                Process millions of operations per second with our optimized AI infrastructure
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="mt-6 text-lg font-medium text-slate-900 dark:text-white">Secure & Compliant</h3>
              <p className="mt-2 text-base text-slate-600 dark:text-slate-300">
                Enterprise-grade security with SOC 2 compliance and end-to-end encryption
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                <svg className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                </svg>
              </div>
              <h3 className="mt-6 text-lg font-medium text-slate-900 dark:text-white">Easy Integration</h3>
              <p className="mt-2 text-base text-slate-600 dark:text-slate-300">
                Connect with your existing tools and workflows in minutes, not months
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white dark:bg-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-slate-900 dark:text-white">99.9%</div>
              <div className="text-sm text-slate-600 dark:text-slate-300">Uptime</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-slate-900 dark:text-white">10M+</div>
              <div className="text-sm text-slate-600 dark:text-slate-300">Operations/day</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-slate-900 dark:text-white">500+</div>
              <div className="text-sm text-slate-600 dark:text-slate-300">Companies</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-slate-900 dark:text-white">&lt;50ms</div>
              <div className="text-sm text-slate-600 dark:text-slate-300">Response time</div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-24 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white">
              Simple, transparent pricing
            </h2>
            <p className="mt-4 text-lg text-slate-600 dark:text-slate-300">
              Choose the plan that's right for your business
            </p>
          </div>

          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Starter Plan */}
            <div className="bg-white dark:bg-slate-900 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-8">
              <h3 className="text-lg font-medium text-slate-900 dark:text-white">Starter</h3>
              <p className="mt-4 text-sm text-slate-600 dark:text-slate-300">Perfect for small teams</p>
              <p className="mt-8">
                <span className="text-4xl font-bold text-slate-900 dark:text-white">$29</span>
                <span className="text-base font-medium text-slate-600 dark:text-slate-300">/month</span>
              </p>
              <ul className="mt-8 space-y-4">
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Up to 10,000 operations</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">5 AI agents</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Email support</span>
                </li>
              </ul>
              <button className="mt-8 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                Start free trial
              </button>
            </div>

            {/* Pro Plan */}
            <div className="bg-white dark:bg-slate-900 rounded-lg shadow-sm border-2 border-blue-600 p-8 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-600 text-white px-3 py-1 text-sm font-medium rounded-full">Most popular</span>
              </div>
              <h3 className="text-lg font-medium text-slate-900 dark:text-white">Pro</h3>
              <p className="mt-4 text-sm text-slate-600 dark:text-slate-300">For growing businesses</p>
              <p className="mt-8">
                <span className="text-4xl font-bold text-slate-900 dark:text-white">$99</span>
                <span className="text-base font-medium text-slate-600 dark:text-slate-300">/month</span>
              </p>
              <ul className="mt-8 space-y-4">
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Up to 100,000 operations</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">25 AI agents</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Priority support</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Advanced analytics</span>
                </li>
              </ul>
              <button className="mt-8 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                Start free trial
              </button>
            </div>

            {/* Enterprise Plan */}
            <div className="bg-white dark:bg-slate-900 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-8">
              <h3 className="text-lg font-medium text-slate-900 dark:text-white">Enterprise</h3>
              <p className="mt-4 text-sm text-slate-600 dark:text-slate-300">For large organizations</p>
              <p className="mt-8">
                <span className="text-4xl font-bold text-slate-900 dark:text-white">Custom</span>
              </p>
              <ul className="mt-8 space-y-4">
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Unlimited operations</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Unlimited AI agents</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">24/7 support</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Custom integrations</span>
                </li>
              </ul>
              <button className="mt-8 w-full border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 py-2 px-4 rounded-md hover:bg-slate-50 dark:hover:bg-slate-700">
                Contact sales
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white">
            Ready to transform your business?
          </h2>
          <p className="mt-4 text-lg text-blue-100">
            Join thousands of companies already using SynapseAI
          </p>
          <div className="mt-8">
            <Link
              to="/register"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-blue-50"
            >
              Start your free trial
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <span className="text-2xl font-bold text-blue-400">SynapseAI</span>
              <p className="mt-4 text-sm text-slate-400">
                AI automation for the modern enterprise
              </p>
            </div>
            <div>
              <h3 className="text-sm font-semibold uppercase tracking-wider">Product</h3>
              <ul className="mt-4 space-y-2">
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">Features</a></li>
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">Pricing</a></li>
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">API</a></li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-semibold uppercase tracking-wider">Company</h3>
              <ul className="mt-4 space-y-2">
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">About</a></li>
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">Blog</a></li>
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">Careers</a></li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-semibold uppercase tracking-wider">Support</h3>
              <ul className="mt-4 space-y-2">
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">Help Center</a></li>
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">Contact</a></li>
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">Status</a></li>
              </ul>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-slate-800 text-center">
            <p className="text-sm text-slate-400">&copy; 2024 SynapseAI. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default LandingPage
