import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { ArrowRightIcon, PlayIcon, Bars3Icon, XMarkIcon, CheckIcon, SparklesIcon } from '@heroicons/react/24/outline'
import { SunIcon, MoonIcon } from '@heroicons/react/24/solid'
import { useTheme } from '@/contexts/ThemeContext'

const LandingPage = () => {
  const { theme, toggleTheme } = useTheme()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [scrollY, setScrollY] = useState(0)

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY)
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <div className="min-h-screen bg-white dark:bg-slate-900">
      {/* Navigation */}
      <nav className={`fixed top-0 w-full z-50 transition-all duration-300 ${scrollY > 50
        ? 'bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-700/50'
        : 'bg-transparent'
        }`}>
        <div className="max-w-6xl mx-auto px-6">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                <SparklesIcon className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-semibold text-slate-900 dark:text-white">SynapseAI</span>
            </div>

            <div className="hidden lg:flex items-center space-x-8">
              <a href="#features" className="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium transition-colors">Product</a>
              <a href="#pricing" className="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium transition-colors">Pricing</a>
              <a href="#customers" className="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium transition-colors">Customers</a>
              <a href="#resources" className="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium transition-colors">Resources</a>
              <button
                onClick={toggleTheme}
                className="p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
              >
                {theme === 'light' ? <MoonIcon className="h-5 w-5 text-slate-600" /> : <SunIcon className="h-5 w-5 text-slate-400" />}
              </button>
              <Link to="/login" className="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium transition-colors">
                Sign in
              </Link>
              <Link to="/register" className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md">
                Get started
              </Link>
            </div>

            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
            >
              {isMenuOpen ? <XMarkIcon className="h-6 w-6" /> : <Bars3Icon className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="lg:hidden bg-white dark:bg-slate-900 border-t border-slate-200 dark:border-slate-700">
            <div className="px-6 py-4 space-y-3">
              <a href="#features" className="block text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium">Product</a>
              <a href="#pricing" className="block text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium">Pricing</a>
              <a href="#customers" className="block text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium">Customers</a>
              <Link to="/login" className="block text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium">Sign in</Link>
              <Link to="/register" className="block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium text-center">Get started</Link>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center px-3 py-1 rounded-full bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-sm font-medium mb-6">
              <SparklesIcon className="w-4 h-4 mr-2" />
              Now available for enterprise
            </div>
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-slate-900 dark:text-white leading-tight mb-6">
              AI that actually
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600">
                understands your business
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-slate-600 dark:text-slate-300 mb-10 leading-relaxed">
              Stop wrestling with generic AI tools. SynapseAI learns your workflows,
              automates your processes, and scales with your team—without the complexity.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Link
                to="/register"
                className="group bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center"
              >
                Start building for free
                <ArrowRightIcon className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
              <button className="group flex items-center px-8 py-4 text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-semibold text-lg transition-colors">
                <div className="w-12 h-12 bg-white dark:bg-slate-800 rounded-full flex items-center justify-center shadow-lg mr-3 group-hover:shadow-xl transition-shadow border border-slate-200 dark:border-slate-700">
                  <PlayIcon className="w-6 h-6 text-blue-600 ml-0.5" />
                </div>
                Watch 2-min demo
              </button>
            </div>
            <div className="text-sm text-slate-500 dark:text-slate-400">
              Free 14-day trial • No credit card required • Cancel anytime
            </div>
          </div>
        </div>
      </section>

      {/* Product Demo Section */}
      <section className="py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900 rounded-3xl p-8 md:p-16 border border-slate-200 dark:border-slate-700">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                See SynapseAI in action
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
                Watch how teams like yours are automating complex workflows and scaling their operations
              </p>
            </div>
            <div className="relative">
              <div className="aspect-video bg-slate-900 rounded-2xl overflow-hidden shadow-2xl">
                <div className="w-full h-full bg-gradient-to-br from-blue-600 to-indigo-700 flex items-center justify-center">
                  <button className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-colors group">
                    <PlayIcon className="w-8 h-8 text-white ml-1 group-hover:scale-110 transition-transform" />
                  </button>
                </div>
              </div>
              <div className="absolute -bottom-4 -right-4 bg-white dark:bg-slate-800 rounded-xl p-4 shadow-lg border border-slate-200 dark:border-slate-700">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-slate-700 dark:text-slate-300">Live demo available</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section id="features" className="py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Everything you need to scale your AI operations
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              From simple automations to complex AI workflows, SynapseAI adapts to your business needs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "🚀",
                title: "Deploy in minutes",
                description: "Get started with pre-built templates or create custom workflows with our visual builder"
              },
              {
                icon: "🔗",
                title: "Connect everything",
                description: "Integrate with 500+ tools including Slack, Salesforce, HubSpot, and your custom APIs"
              },
              {
                icon: "🧠",
                title: "Smart automation",
                description: "AI that learns from your data patterns and suggests optimizations automatically"
              },
              {
                icon: "📊",
                title: "Real-time insights",
                description: "Monitor performance, track ROI, and get actionable insights from your AI operations"
              },
              {
                icon: "🔒",
                title: "Enterprise security",
                description: "SOC 2 Type II certified with end-to-end encryption and compliance controls"
              },
              {
                icon: "⚡",
                title: "Lightning fast",
                description: "Process millions of operations with 99.9% uptime and sub-50ms response times"
              }
            ].map((feature, index) => (
              <div key={index} className="group p-6 rounded-2xl bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 hover:shadow-lg">
                <div className="text-3xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3">
                  {feature.title}
                </h3>
                <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Social Proof */}
      <section className="py-16 px-6 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <p className="text-sm font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider mb-8">
              Trusted by teams at
            </p>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-8 items-center opacity-60">
              {['Microsoft', 'Shopify', 'Stripe', 'Notion', 'Figma'].map((company) => (
                <div key={company} className="text-center">
                  <div className="text-2xl font-bold text-slate-400 dark:text-slate-500">{company}</div>
                </div>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-16">
            <div className="bg-white dark:bg-slate-900 rounded-2xl p-8 border border-slate-200 dark:border-slate-700">
              <div className="flex items-center mb-6">
                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face" alt="Sarah Chen" className="w-12 h-12 rounded-full mr-4" />
                <div>
                  <div className="font-semibold text-slate-900 dark:text-white">Sarah Chen</div>
                  <div className="text-sm text-slate-500 dark:text-slate-400">VP of Operations, TechFlow</div>
                </div>
              </div>
              <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                "SynapseAI reduced our manual processing time by 80%. What used to take our team hours now happens automatically in minutes."
              </p>
            </div>

            <div className="bg-white dark:bg-slate-900 rounded-2xl p-8 border border-slate-200 dark:border-slate-700">
              <div className="flex items-center mb-6">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face" alt="Marcus Rodriguez" className="w-12 h-12 rounded-full mr-4" />
                <div>
                  <div className="font-semibold text-slate-900 dark:text-white">Marcus Rodriguez</div>
                  <div className="text-sm text-slate-500 dark:text-slate-400">CTO, DataScale</div>
                </div>
              </div>
              <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                "The most intuitive AI platform we've used. Our developers were productive from day one, no extensive training needed."
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white dark:bg-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-slate-900 dark:text-white">99.9%</div>
              <div className="text-sm text-slate-600 dark:text-slate-300">Uptime</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-slate-900 dark:text-white">10M+</div>
              <div className="text-sm text-slate-600 dark:text-slate-300">Operations/day</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-slate-900 dark:text-white">500+</div>
              <div className="text-sm text-slate-600 dark:text-slate-300">Companies</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-slate-900 dark:text-white">&lt;50ms</div>
              <div className="text-sm text-slate-600 dark:text-slate-300">Response time</div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-24 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white">
              Simple, transparent pricing
            </h2>
            <p className="mt-4 text-lg text-slate-600 dark:text-slate-300">
              Choose the plan that's right for your business
            </p>
          </div>

          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Starter Plan */}
            <div className="bg-white dark:bg-slate-900 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-8">
              <h3 className="text-lg font-medium text-slate-900 dark:text-white">Starter</h3>
              <p className="mt-4 text-sm text-slate-600 dark:text-slate-300">Perfect for small teams</p>
              <p className="mt-8">
                <span className="text-4xl font-bold text-slate-900 dark:text-white">$29</span>
                <span className="text-base font-medium text-slate-600 dark:text-slate-300">/month</span>
              </p>
              <ul className="mt-8 space-y-4">
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Up to 10,000 operations</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">5 AI agents</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Email support</span>
                </li>
              </ul>
              <button className="mt-8 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                Start free trial
              </button>
            </div>

            {/* Pro Plan */}
            <div className="bg-white dark:bg-slate-900 rounded-lg shadow-sm border-2 border-blue-600 p-8 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-600 text-white px-3 py-1 text-sm font-medium rounded-full">Most popular</span>
              </div>
              <h3 className="text-lg font-medium text-slate-900 dark:text-white">Pro</h3>
              <p className="mt-4 text-sm text-slate-600 dark:text-slate-300">For growing businesses</p>
              <p className="mt-8">
                <span className="text-4xl font-bold text-slate-900 dark:text-white">$99</span>
                <span className="text-base font-medium text-slate-600 dark:text-slate-300">/month</span>
              </p>
              <ul className="mt-8 space-y-4">
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Up to 100,000 operations</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">25 AI agents</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Priority support</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Advanced analytics</span>
                </li>
              </ul>
              <button className="mt-8 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                Start free trial
              </button>
            </div>

            {/* Enterprise Plan */}
            <div className="bg-white dark:bg-slate-900 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-8">
              <h3 className="text-lg font-medium text-slate-900 dark:text-white">Enterprise</h3>
              <p className="mt-4 text-sm text-slate-600 dark:text-slate-300">For large organizations</p>
              <p className="mt-8">
                <span className="text-4xl font-bold text-slate-900 dark:text-white">Custom</span>
              </p>
              <ul className="mt-8 space-y-4">
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Unlimited operations</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Unlimited AI agents</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">24/7 support</span>
                </li>
                <li className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm text-slate-600 dark:text-slate-300">Custom integrations</span>
                </li>
              </ul>
              <button className="mt-8 w-full border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 py-2 px-4 rounded-md hover:bg-slate-50 dark:hover:bg-slate-700">
                Contact sales
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white">
            Ready to transform your business?
          </h2>
          <p className="mt-4 text-lg text-blue-100">
            Join thousands of companies already using SynapseAI
          </p>
          <div className="mt-8">
            <Link
              to="/register"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-blue-50"
            >
              Start your free trial
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <span className="text-2xl font-bold text-blue-400">SynapseAI</span>
              <p className="mt-4 text-sm text-slate-400">
                AI automation for the modern enterprise
              </p>
            </div>
            <div>
              <h3 className="text-sm font-semibold uppercase tracking-wider">Product</h3>
              <ul className="mt-4 space-y-2">
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">Features</a></li>
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">Pricing</a></li>
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">API</a></li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-semibold uppercase tracking-wider">Company</h3>
              <ul className="mt-4 space-y-2">
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">About</a></li>
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">Blog</a></li>
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">Careers</a></li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-semibold uppercase tracking-wider">Support</h3>
              <ul className="mt-4 space-y-2">
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">Help Center</a></li>
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">Contact</a></li>
                <li><a href="#" className="text-sm text-slate-400 hover:text-white">Status</a></li>
              </ul>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-slate-800 text-center">
            <p className="text-sm text-slate-400">&copy; 2024 SynapseAI. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default LandingPage
