import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { ArrowRightIcon, PlayIcon, Bars3Icon, XMarkIcon, CheckIcon, SparklesIcon } from '@heroicons/react/24/outline'
import { SunIcon, MoonIcon } from '@heroicons/react/24/solid'
import { useTheme } from '@/contexts/ThemeContext'

const LandingPage = () => {
  const { theme, toggleTheme } = useTheme()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [scrollY, setScrollY] = useState(0)

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY)
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <div className="min-h-screen bg-white dark:bg-slate-900">
      {/* Navigation */}
      <nav className={`fixed top-0 w-full z-50 transition-all duration-300 ${scrollY > 50
        ? 'bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-700/50'
        : 'bg-transparent'
        }`}>
        <div className="max-w-6xl mx-auto px-6">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                <SparklesIcon className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-semibold text-slate-900 dark:text-white">SynapseAI</span>
            </div>

            <div className="hidden lg:flex items-center space-x-8">
              <a href="#features" className="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium transition-colors">Product</a>
              <a href="#pricing" className="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium transition-colors">Pricing</a>
              <a href="#customers" className="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium transition-colors">Customers</a>
              <a href="#resources" className="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium transition-colors">Resources</a>
              <button
                onClick={toggleTheme}
                className="p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
              >
                {theme === 'light' ? <MoonIcon className="h-5 w-5 text-slate-600" /> : <SunIcon className="h-5 w-5 text-slate-400" />}
              </button>
              <Link to="/login" className="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium transition-colors">
                Sign in
              </Link>
              <Link to="/register" className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md">
                Get started
              </Link>
            </div>

            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
            >
              {isMenuOpen ? <XMarkIcon className="h-6 w-6" /> : <Bars3Icon className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="lg:hidden bg-white dark:bg-slate-900 border-t border-slate-200 dark:border-slate-700">
            <div className="px-6 py-4 space-y-3">
              <a href="#features" className="block text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium">Product</a>
              <a href="#pricing" className="block text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium">Pricing</a>
              <a href="#customers" className="block text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium">Customers</a>
              <Link to="/login" className="block text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-medium">Sign in</Link>
              <Link to="/register" className="block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium text-center">Get started</Link>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center px-3 py-1 rounded-full bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-sm font-medium mb-6">
              <SparklesIcon className="w-4 h-4 mr-2" />
              Now available for enterprise
            </div>
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-slate-900 dark:text-white leading-tight mb-6">
              AI that actually
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600">
                understands your business
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-slate-600 dark:text-slate-300 mb-10 leading-relaxed">
              Stop wrestling with generic AI tools. SynapseAI learns your workflows,
              automates your processes, and scales with your team—without the complexity.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Link
                to="/register"
                className="group bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center"
              >
                Start building for free
                <ArrowRightIcon className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
              <button className="group flex items-center px-8 py-4 text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-semibold text-lg transition-colors">
                <div className="w-12 h-12 bg-white dark:bg-slate-800 rounded-full flex items-center justify-center shadow-lg mr-3 group-hover:shadow-xl transition-shadow border border-slate-200 dark:border-slate-700">
                  <PlayIcon className="w-6 h-6 text-blue-600 ml-0.5" />
                </div>
                Watch 2-min demo
              </button>
            </div>
            <div className="text-sm text-slate-500 dark:text-slate-400">
              Free 14-day trial • No credit card required • Cancel anytime
            </div>
          </div>
        </div>
      </section>

      {/* Product Demo Section */}
      <section className="py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900 rounded-3xl p-8 md:p-16 border border-slate-200 dark:border-slate-700">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                See SynapseAI in action
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
                Watch how teams like yours are automating complex workflows and scaling their operations
              </p>
            </div>
            <div className="relative">
              <div className="aspect-video bg-slate-900 rounded-2xl overflow-hidden shadow-2xl">
                <div className="w-full h-full bg-gradient-to-br from-blue-600 to-indigo-700 flex items-center justify-center">
                  <button className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-colors group">
                    <PlayIcon className="w-8 h-8 text-white ml-1 group-hover:scale-110 transition-transform" />
                  </button>
                </div>
              </div>
              <div className="absolute -bottom-4 -right-4 bg-white dark:bg-slate-800 rounded-xl p-4 shadow-lg border border-slate-200 dark:border-slate-700">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-slate-700 dark:text-slate-300">Live demo available</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section id="features" className="py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Everything you need to scale your AI operations
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              From simple automations to complex AI workflows, SynapseAI adapts to your business needs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "🚀",
                title: "Deploy in minutes",
                description: "Get started with pre-built templates or create custom workflows with our visual builder"
              },
              {
                icon: "🔗",
                title: "Connect everything",
                description: "Integrate with 500+ tools including Slack, Salesforce, HubSpot, and your custom APIs"
              },
              {
                icon: "🧠",
                title: "Smart automation",
                description: "AI that learns from your data patterns and suggests optimizations automatically"
              },
              {
                icon: "📊",
                title: "Real-time insights",
                description: "Monitor performance, track ROI, and get actionable insights from your AI operations"
              },
              {
                icon: "🔒",
                title: "Enterprise security",
                description: "SOC 2 Type II certified with end-to-end encryption and compliance controls"
              },
              {
                icon: "⚡",
                title: "Lightning fast",
                description: "Process millions of operations with 99.9% uptime and sub-50ms response times"
              }
            ].map((feature, index) => (
              <div key={index} className="group p-6 rounded-2xl bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 hover:shadow-lg">
                <div className="text-3xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3">
                  {feature.title}
                </h3>
                <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Social Proof */}
      <section className="py-16 px-6 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <p className="text-sm font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider mb-8">
              Trusted by teams at
            </p>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-8 items-center opacity-60">
              {['Microsoft', 'Shopify', 'Stripe', 'Notion', 'Figma'].map((company) => (
                <div key={company} className="text-center">
                  <div className="text-2xl font-bold text-slate-400 dark:text-slate-500">{company}</div>
                </div>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-16">
            <div className="bg-white dark:bg-slate-900 rounded-2xl p-8 border border-slate-200 dark:border-slate-700">
              <div className="flex items-center mb-6">
                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face" alt="Sarah Chen" className="w-12 h-12 rounded-full mr-4" />
                <div>
                  <div className="font-semibold text-slate-900 dark:text-white">Sarah Chen</div>
                  <div className="text-sm text-slate-500 dark:text-slate-400">VP of Operations, TechFlow</div>
                </div>
              </div>
              <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                "SynapseAI reduced our manual processing time by 80%. What used to take our team hours now happens automatically in minutes."
              </p>
            </div>

            <div className="bg-white dark:bg-slate-900 rounded-2xl p-8 border border-slate-200 dark:border-slate-700">
              <div className="flex items-center mb-6">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face" alt="Marcus Rodriguez" className="w-12 h-12 rounded-full mr-4" />
                <div>
                  <div className="font-semibold text-slate-900 dark:text-white">Marcus Rodriguez</div>
                  <div className="text-sm text-slate-500 dark:text-slate-400">CTO, DataScale</div>
                </div>
              </div>
              <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                "The most intuitive AI platform we've used. Our developers were productive from day one, no extensive training needed."
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white dark:bg-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-slate-900 dark:text-white">99.9%</div>
              <div className="text-sm text-slate-600 dark:text-slate-300">Uptime</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-slate-900 dark:text-white">10M+</div>
              <div className="text-sm text-slate-600 dark:text-slate-300">Operations/day</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-slate-900 dark:text-white">500+</div>
              <div className="text-sm text-slate-600 dark:text-slate-300">Companies</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-slate-900 dark:text-white">&lt;50ms</div>
              <div className="text-sm text-slate-600 dark:text-slate-300">Response time</div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Choose your plan
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Start free and scale as you grow. All plans include our core AI automation features.
            </p>
            <div className="mt-8 inline-flex items-center bg-slate-100 dark:bg-slate-800 rounded-xl p-1">
              <button className="px-4 py-2 text-sm font-medium text-slate-600 dark:text-slate-300 rounded-lg">Monthly</button>
              <button className="px-4 py-2 text-sm font-medium bg-white dark:bg-slate-700 text-slate-900 dark:text-white rounded-lg shadow-sm">
                Annual <span className="ml-1 text-xs bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 px-2 py-0.5 rounded-full">Save 20%</span>
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Starter */}
            <div className="bg-white dark:bg-slate-800 rounded-2xl border border-slate-200 dark:border-slate-700 p-8 hover:border-blue-300 dark:hover:border-blue-600 transition-colors">
              <div className="mb-8">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">Starter</h3>
                <p className="text-slate-600 dark:text-slate-300 mb-6">Perfect for individuals and small teams</p>
                <div className="flex items-baseline">
                  <span className="text-4xl font-bold text-slate-900 dark:text-white">$0</span>
                  <span className="text-slate-500 dark:text-slate-400 ml-2">/month</span>
                </div>
                <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">Free forever</p>
              </div>

              <ul className="space-y-4 mb-8">
                {[
                  "Up to 1,000 operations/month",
                  "3 AI agents",
                  "Basic integrations",
                  "Community support",
                  "Standard templates"
                ].map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <CheckIcon className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-slate-600 dark:text-slate-300">{feature}</span>
                  </li>
                ))}
              </ul>

              <Link to="/register" className="w-full bg-slate-900 dark:bg-white text-white dark:text-slate-900 py-3 px-4 rounded-xl font-semibold hover:bg-slate-800 dark:hover:bg-slate-100 transition-colors text-center block">
                Get started free
              </Link>
            </div>

            {/* Pro */}
            <div className="bg-white dark:bg-slate-800 rounded-2xl border-2 border-blue-600 p-8 relative shadow-lg">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold">Most popular</span>
              </div>

              <div className="mb-8">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">Pro</h3>
                <p className="text-slate-600 dark:text-slate-300 mb-6">For growing teams and businesses</p>
                <div className="flex items-baseline">
                  <span className="text-4xl font-bold text-slate-900 dark:text-white">$49</span>
                  <span className="text-slate-500 dark:text-slate-400 ml-2">/month</span>
                </div>
                <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">Billed annually or $59 monthly</p>
              </div>

              <ul className="space-y-4 mb-8">
                {[
                  "Up to 50,000 operations/month",
                  "15 AI agents",
                  "Advanced integrations",
                  "Priority support",
                  "Custom workflows",
                  "Analytics dashboard",
                  "API access"
                ].map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <CheckIcon className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-slate-600 dark:text-slate-300">{feature}</span>
                  </li>
                ))}
              </ul>

              <Link to="/register" className="w-full bg-blue-600 text-white py-3 px-4 rounded-xl font-semibold hover:bg-blue-700 transition-colors text-center block">
                Start 14-day free trial
              </Link>
            </div>

            {/* Enterprise */}
            <div className="bg-white dark:bg-slate-800 rounded-2xl border border-slate-200 dark:border-slate-700 p-8 hover:border-blue-300 dark:hover:border-blue-600 transition-colors">
              <div className="mb-8">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">Enterprise</h3>
                <p className="text-slate-600 dark:text-slate-300 mb-6">For large organizations with custom needs</p>
                <div className="flex items-baseline">
                  <span className="text-4xl font-bold text-slate-900 dark:text-white">Custom</span>
                </div>
                <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">Volume pricing available</p>
              </div>

              <ul className="space-y-4 mb-8">
                {[
                  "Unlimited operations",
                  "Unlimited AI agents",
                  "Custom integrations",
                  "24/7 dedicated support",
                  "On-premise deployment",
                  "Advanced security",
                  "Custom SLA",
                  "Training & onboarding"
                ].map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <CheckIcon className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-slate-600 dark:text-slate-300">{feature}</span>
                  </li>
                ))}
              </ul>

              <button className="w-full border-2 border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 py-3 px-4 rounded-xl font-semibold hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors">
                Contact sales
              </button>
            </div>
          </div>

          <div className="mt-16 text-center">
            <p className="text-slate-600 dark:text-slate-300 mb-6">
              All plans include 99.9% uptime SLA, SOC 2 compliance, and migration support
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <a href="#" className="text-blue-600 hover:text-blue-700 font-medium">Compare all features →</a>
              <a href="#" className="text-blue-600 hover:text-blue-700 font-medium">Talk to sales</a>
              <a href="#" className="text-blue-600 hover:text-blue-700 font-medium">See customer stories</a>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 px-6 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to 10x your productivity?
          </h2>
          <p className="text-xl text-blue-100 mb-10 leading-relaxed">
            Join 10,000+ teams already using SynapseAI to automate their workflows and scale their operations.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
            <Link
              to="/register"
              className="group bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-blue-50 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center"
            >
              Start building for free
              <ArrowRightIcon className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Link>
            <button className="text-white border-2 border-white/30 hover:border-white/50 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/10 transition-all duration-200">
              Schedule a demo
            </button>
          </div>
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center text-blue-100 text-sm">
            <div className="flex items-center">
              <CheckIcon className="h-4 w-4 mr-2" />
              14-day free trial
            </div>
            <div className="flex items-center">
              <CheckIcon className="h-4 w-4 mr-2" />
              No credit card required
            </div>
            <div className="flex items-center">
              <CheckIcon className="h-4 w-4 mr-2" />
              Cancel anytime
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 text-white">
        <div className="max-w-6xl mx-auto px-6 py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
            {/* Company Info */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-2 mb-6">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                  <SparklesIcon className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-semibold">SynapseAI</span>
              </div>
              <p className="text-slate-400 mb-6 max-w-md">
                The AI automation platform that learns your business and scales with your team.
                Trusted by 10,000+ companies worldwide.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="w-10 h-10 bg-slate-800 rounded-lg flex items-center justify-center hover:bg-slate-700 transition-colors">
                  <span className="sr-only">Twitter</span>
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
                <a href="#" className="w-10 h-10 bg-slate-800 rounded-lg flex items-center justify-center hover:bg-slate-700 transition-colors">
                  <span className="sr-only">LinkedIn</span>
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                  </svg>
                </a>
                <a href="#" className="w-10 h-10 bg-slate-800 rounded-lg flex items-center justify-center hover:bg-slate-700 transition-colors">
                  <span className="sr-only">GitHub</span>
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                  </svg>
                </a>
              </div>
            </div>

            {/* Product */}
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-3 text-slate-400">
                <li><a href="#" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Integrations</a></li>
                <li><a href="#" className="hover:text-white transition-colors">API</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Changelog</a></li>
              </ul>
            </div>

            {/* Company */}
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-3 text-slate-400">
                <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Press</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Partners</a></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-3 text-slate-400">
                <li><a href="#" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Status</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Security</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-slate-800 mt-12 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-slate-400 text-sm">
                &copy; 2024 SynapseAI, Inc. All rights reserved.
              </p>
              <div className="flex space-x-6 mt-4 md:mt-0">
                <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Privacy Policy</a>
                <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Terms of Service</a>
                <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Cookie Policy</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default LandingPage
