import React, { useState, useEffect } from 'react'
import {  useNavigate } from 'react-router-dom'
import { Plus, Search, MoreVertical, Trash2, Eye, Play, Square, Clock } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { Session, PaginatedResponse } from '@/types'
import { api } from '@/services/api'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Badge } from '@/components/ui/Badge'
import { Card, CardContent } from '@/components/ui/Card'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/DropdownMenu'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/Dialog'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { formatRelativeTime, getStatusColor } from '@/lib/utils'

const SessionsPage: React.FC = () => {
  const navigate = useNavigate()
  const [sessions, setSessions] = useState<Session[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('ALL')
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [sessionToDelete, setSessionToDelete] = useState<Session | null>(null)
  const [deleting, setDeleting] = useState(false)
  const [pagination, setPagination] = useState({
    limit: 20,
    offset: 0,
    total: 0
  })

  useEffect(() => {
    loadSessions()
  }, [pagination.offset, statusFilter])

  const loadSessions = async () => {
    try {
      setLoading(true)
      const params: any = {
        limit: pagination.limit,
        offset: pagination.offset
      }
      
      if (statusFilter !== 'ALL') {
        params.status = statusFilter
      }

      const response: PaginatedResponse<Session> = await api.getSessions(params)
      setSessions(response.items ?? response.data ?? [])
      setPagination(prev => ({
        ...prev,
        total: response.pagination?.total ?? response.total ?? 0
      }))
    } catch (error) {
      console.error('Failed to load sessions:', error)
      toast.error('Failed to load sessions')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteSession = async () => {
    if (!sessionToDelete) return

    try {
      setDeleting(true)
      await api.deleteSession(sessionToDelete.id)
      setSessions(sessions.filter(session => session.id !== sessionToDelete.id))
      toast.success('Session deleted successfully')
      setDeleteDialogOpen(false)
      setSessionToDelete(null)
    } catch (error) {
      console.error('Failed to delete session:', error)
      toast.error('Failed to delete session')
    } finally {
      setDeleting(false)
    }
  }

  const handleStartAgent = async (session: Session) => {
    if (!session.agentId) {
      toast.error('No agent assigned to this session')
      return
    }

    try {
      await api.startAgentInSession(session.id, session.agentId)
      toast.success('Agent started successfully')
      loadSessions() // Refresh to get updated status
    } catch (error) {
      console.error('Failed to start agent:', error)
      toast.error('Failed to start agent')
    }
  }

  const handleStopAgent = async (session: Session) => {
    try {
      await api.stopAgentInSession(session.id)
      toast.success('Agent stopped successfully')
      loadSessions() // Refresh to get updated status
    } catch (error) {
      console.error('Failed to stop agent:', error)
      toast.error('Failed to stop agent')
    }
  }

  const filteredSessions = sessions.filter(session => {
    const matchesSearch = session.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         session.agent?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         session.provider?.name.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesSearch
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800'
      case 'INACTIVE': return 'bg-gray-100 text-gray-800'
      case 'EXPIRED': return 'bg-yellow-100 text-yellow-800'
      case 'TERMINATED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDuration = (createdAt: string, expiresAt?: string | null) => {
    const start = new Date(createdAt)
    const end = expiresAt ? new Date(expiresAt) : new Date()
    const duration = end.getTime() - start.getTime()
    const hours = Math.floor(duration / (1000 * 60 * 60))
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))
    return `${hours}h ${minutes}m`
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Sessions</h1>
          <p className="text-gray-600 mt-1">Manage active and historical AI sessions</p>
        </div>
        <Button onClick={() => navigate('/sessions/new')} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          New Session
        </Button>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search sessions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="ALL">All Status</option>
          <option value="ACTIVE">Active</option>
          <option value="INACTIVE">Inactive</option>
          <option value="EXPIRED">Expired</option>
          <option value="TERMINATED">Terminated</option>
        </select>
      </div>

      {/* Sessions List */}
      <div className="space-y-4">
        {filteredSessions.map((session) => (
          <Card key={session.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">
                      Session {session.id.slice(0, 8)}...
                    </h3>
                    <Badge className={getStatusColor(session.status)}>
                      {session.status}
                    </Badge>
                    {session.agent && (
                      <Badge variant="default">
                        {session.agent.name}
                      </Badge>
                    )}
                    {session.provider && (
                      <Badge variant="default">
                        {session.provider.name}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>Duration: {formatDuration(session.createdAt, session.expiresAt)}</span>
                    </div>
                    <div>
                      <span>Events: {session._count?.events || 0}</span>
                    </div>
                    <div>
                      <span>Created: {formatRelativeTime(session.createdAt)}</span>
                    </div>
                    <div>
                      <span>Updated: {formatRelativeTime(session.updatedAt)}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {session.status === 'ACTIVE' && session.agentId && (
                    <Button variant="outline" size="sm" onClick={() => handleStopAgent(session)}>
                      <Square className="h-4 w-4 mr-1" />
                      Stop
                    </Button>
                  )}
                  {session.status === 'INACTIVE' && session.agentId && (
                    <Button variant="outline" size="sm" onClick={() => handleStartAgent(session)}>
                      <Play className="h-4 w-4 mr-1" />
                      Start
                    </Button>
                  )}
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => navigate(`/sessions/${session.id}`)}>
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => {
                          setSessionToDelete(session)
                          setDeleteDialogOpen(true)
                        }}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600">
          Showing {pagination.offset + 1} to {Math.min(pagination.offset + pagination.limit, pagination.total)} of {pagination.total} sessions
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            disabled={pagination.offset === 0}
            onClick={() => setPagination(prev => ({ ...prev, offset: Math.max(0, prev.offset - prev.limit) }))}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            disabled={pagination.offset + pagination.limit >= pagination.total}
            onClick={() => setPagination(prev => ({ ...prev, offset: prev.offset + prev.limit }))}
          >
            Next
          </Button>
        </div>
      </div>

      {filteredSessions.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-2">No sessions found</div>
          <p className="text-gray-600 mb-4">
            {searchTerm || statusFilter !== 'ALL' 
              ? 'Try adjusting your search or filters' 
              : 'Get started by creating your first session'
            }
          </p>
          {!searchTerm && statusFilter === 'ALL' && (
            <Button onClick={() => navigate('/sessions/new')}>
              Create Your First Session
            </Button>
          )}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Session</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this session? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteSession}
              disabled={deleting}
            >
              {deleting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default SessionsPage
